import type { NextRequest } from "next/server";
import { createServerSupabaseClient } from "./supabase";
import type { User } from "@supabase/supabase-js";

export interface UserProfile {
  id: string;
  user_id: string;
  email: string;
  full_name: string | null;
  role: 'admin' | 'user';
  created_at: string;
  updated_at: string;
}

export async function createContext(req: NextRequest) {
  let user: User | null = null;
  let userProfile: UserProfile | null = null;
  
  try {
    const supabase = await createServerSupabaseClient();
    const { data: { user: authUser } } = await supabase.auth.getUser();
    user = authUser;
    
    // If user is authenticated, fetch their profile with role
    if (user) {
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();
      
      userProfile = profile as UserProfile;
    }
  } catch (error) {
    console.error("Error getting user session:", error);
    // Fail silently to not break non-auth operations
  }
  
  return {
    user,
    userProfile,
    session: user ? { user } : null,
  };
}

export type Context = Awaited<ReturnType<typeof createContext>>;