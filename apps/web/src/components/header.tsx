"use client";
import Link from "next/link";
import { trpc } from "@/utils/trpc";
import { AuthButton } from "@/components/auth/auth-button";
import { Button } from "@/components/ui/button";
import { BarChart3, Zap, Shield } from "lucide-react";

export default function Header() {
  const { data: user } = trpc.getCurrentUser.useQuery();
  
  // Get user profile to check if admin (only if user is authenticated)
  const userProfileQuery = trpc.getUserProfile.useQuery(undefined, {
    enabled: !!user,
    retry: false,
  });
  
  const isAdmin = userProfileQuery.data?.role === 'admin';
  
  return (
    <header className="sticky top-0 z-50 w-full glass-effect border-b border-border/30">
      <div className="content-container">
        <div className="center-all justify-between h-20">
          {/* Brand */}
          <Link href="/" className="hover:opacity-90 transition-opacity duration-300">
            <span className="font-bold text-2xl tracking-tight text-attention-blue">ATTENTION</span>
          </Link>
          
          {/* Navigation */}
          <div className="flex items-center gap-4">
            {/* Dashboard Link */}
            <Link href="/dashboard">
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Dashboard
              </Button>
            </Link>
            
            {/* Admin Link - only show for admin users */}
            {isAdmin && (
              <Link href="/admin">
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Admin
                </Button>
              </Link>
            )}
            
            {/* Auth Button */}
            <AuthButton user={user} />
          </div>
        </div>
      </div>
    </header>
  );
}
