"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { trpc } from "@/utils/trpc";
import { AuthButton } from "@/components/auth/auth-button";
import { Button } from "@/components/ui/button";
import { Bar<PERSON>hart3, Zap, Shield } from "lucide-react";

export default function Header() {
  const pathname = usePathname();
  const { data: user } = trpc.getCurrentUser.useQuery();

  // Get user profile to check if admin (only if user is authenticated)
  const userProfileQuery = trpc.getUserProfile.useQuery(undefined, {
    enabled: !!user,
    retry: false,
  });

  const isAdmin = userProfileQuery.data?.role === 'admin';

  // Determine which page we're on
  const isLandingPage = pathname === '/';
  const isDashboardPage = pathname === '/dashboard';

  console.log('Header: Current pathname:', pathname, 'isLandingPage:', isLandingPage, 'isDashboardPage:', isDashboardPage);
  
  return (
    <header className="sticky top-0 z-50 w-full glass-effect border-b border-border/30">
      <div className="content-container">
        <div className="center-all justify-between h-20">
          {/* Brand */}
          <Link href="/" className="hover:opacity-90 transition-opacity duration-300">
            <span className="font-bold text-2xl tracking-tight text-attention-blue">ATTENTION</span>
          </Link>
          
          {/* Navigation */}
          <div className="flex items-center gap-4">
            {/* Conditional Navigation based on current page */}
            {isLandingPage && (
              /* Show Dashboard button on landing page */
              <Link href="/dashboard">
                <Button variant="default" className="flex items-center space-x-2">
                  <BarChart3 className="h-4 w-4" />
                  <span>Dashboard</span>
                </Button>
              </Link>
            )}

            {isDashboardPage && (
              /* Show Sign In button on dashboard page */
              <AuthButton user={user} />
            )}

            {/* Admin Link - only show for admin users */}
            {isAdmin && (
              <Link href="/admin">
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Admin
                </Button>
              </Link>
            )}

            {/* Show auth button on other pages (not landing or dashboard) */}
            {!isLandingPage && !isDashboardPage && (
              <AuthButton user={user} />
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
