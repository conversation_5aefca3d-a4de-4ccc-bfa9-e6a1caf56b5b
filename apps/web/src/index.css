@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:where(.dark, .dark *));

@theme {
  --font-sans: "<PERSON>", "<PERSON><PERSON><PERSON>", ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

html,
body {
  @apply bg-white dark:bg-gray-950;

  @media (prefers-color-scheme: dark) {
    color-scheme: dark;
  }
}

:root {
  --radius: 0.625rem;
  
  /* Tech Vibes Color Palette */
  /* Background: #fffffe → Light off-white */
  --background: oklch(99.9% 0.001 0);
  --foreground: oklch(27% 0.05 250); /* #272343 → Headline dark blue */
  
  /* Cards and surfaces */
  --card: oklch(99.9% 0.001 0); /* Same as background */
  --card-foreground: oklch(27% 0.05 250);
  
  /* Popovers */
  --popover: oklch(99.9% 0.001 0);
  --popover-foreground: oklch(27% 0.05 250);
  
  /* Primary - Using the bright yellow accent */
  --primary: oklch(88% 0.15 95); /* #ffd803 → Bright yellow */
  --primary-foreground: oklch(27% 0.05 250); /* Dark blue text on yellow */
  
  /* Secondary - Using the light mint */
  --secondary: oklch(95% 0.02 180); /* #e3f6f5 → Light mint */
  --secondary-foreground: oklch(27% 0.05 250);
  
  /* Muted areas */
  --muted: oklch(95% 0.02 180); /* Light mint for muted areas */
  --muted-foreground: oklch(35% 0.04 245); /* #2d334a → Paragraph color */
  
  /* Accent - Tertiary teal */
  --accent: oklch(85% 0.08 180); /* #bae8e8 → Light teal */
  --accent-foreground: oklch(27% 0.05 250);
  
  /* Destructive */
  --destructive: oklch(58% 0.2 25);
  
  /* Borders and inputs */
  --border: oklch(90% 0.02 180); /* Subtle teal tint */
  --input: oklch(90% 0.02 180);
  --ring: oklch(88% 0.15 95); /* Yellow focus ring */
  
  /* Chart colors - tech vibes palette */
  --chart-1: oklch(88% 0.15 95); /* Yellow */
  --chart-2: oklch(85% 0.08 180); /* Light teal */
  --chart-3: oklch(27% 0.05 250); /* Dark blue */
  --chart-4: oklch(95% 0.02 180); /* Light mint */
  --chart-5: oklch(35% 0.04 245); /* Dark gray-blue */
  
  /* Custom light blue for ATTENTION text */
  --attention-blue: oklch(65% 0.15 240); /* Light blue */
  
  /* Sidebar */
  --sidebar: oklch(99.9% 0.001 0);
  --sidebar-foreground: oklch(27% 0.05 250);
  --sidebar-primary: oklch(88% 0.15 95); /* Yellow accent */
  --sidebar-primary-foreground: oklch(27% 0.05 250);
  --sidebar-accent: oklch(95% 0.02 180);
  --sidebar-accent-foreground: oklch(27% 0.05 250);
  --sidebar-border: oklch(90% 0.02 180);
  --sidebar-ring: oklch(88% 0.15 95);
}

.dark {
  /* Rich dark mode with better contrast */
  --background: oklch(8% 0.01 250); /* Much darker background */
  --foreground: oklch(98% 0.01 0); /* Pure white text */
  
  --card: oklch(12% 0.02 250); /* Dark cards with subtle blue tint */
  --card-foreground: oklch(95% 0.01 0);
  
  --popover: oklch(10% 0.02 250);
  --popover-foreground: oklch(98% 0.01 0);
  
  /* Bright yellow accent for dark mode */
  --primary: oklch(85% 0.18 95); /* More vibrant yellow */
  --primary-foreground: oklch(10% 0.02 250);
  
  --secondary: oklch(18% 0.03 250); /* Dark blue-teal */
  --secondary-foreground: oklch(90% 0.02 180);
  
  --muted: oklch(15% 0.02 250);
  --muted-foreground: oklch(65% 0.02 0); /* Neutral gray */
  
  --accent: oklch(75% 0.12 180); /* Bright teal accent */
  --accent-foreground: oklch(10% 0.02 250);
  
  --destructive: oklch(68% 0.22 25);
  
  --border: oklch(22% 0.02 250);
  --input: oklch(15% 0.02 250);
  --ring: oklch(85% 0.18 95);
  
  /* Vibrant dark mode chart colors */
  --chart-1: oklch(85% 0.18 95); /* Bright yellow */
  --chart-2: oklch(75% 0.12 180); /* Bright teal */
  --chart-3: oklch(70% 0.15 250); /* Bright blue */
  --chart-4: oklch(80% 0.10 160); /* Light teal */
  --chart-5: oklch(78% 0.08 200); /* Cyan */
  
  /* Custom light blue for ATTENTION text - same in dark mode */
  --attention-blue: oklch(70% 0.15 240); /* Slightly brighter in dark mode */
  
  --sidebar: oklch(10% 0.02 250);
  --sidebar-foreground: oklch(95% 0.01 0);
  --sidebar-primary: oklch(85% 0.18 95);
  --sidebar-primary-foreground: oklch(10% 0.02 250);
  --sidebar-accent: oklch(18% 0.03 250);
  --sidebar-accent-foreground: oklch(90% 0.02 180);
  --sidebar-border: oklch(22% 0.02 250);
  --sidebar-ring: oklch(85% 0.18 95);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-attention-blue: var(--attention-blue);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Responsive container with perfect centering */
  .content-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }
  
  @media (min-width: 640px) {
    .content-container {
      padding: 0 1.5rem;
    }
  }
  
  @media (min-width: 1024px) {
    .content-container {
      padding: 0 2rem;
    }
  }
  
  /* Perfect centering utility */
  .center-all {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* Tech vibes gradient backgrounds */
  .gradient-tech {
    background: linear-gradient(135deg, 
      oklch(99.9% 0.001 0) 0%, 
      oklch(95% 0.02 180) 100%);
  }
  
  .dark .gradient-tech {
    background: linear-gradient(135deg, 
      oklch(8% 0.01 250) 0%, 
      oklch(12% 0.02 250) 100%);
  }
  
  /* Modern glass effect with better dark mode */
  .glass-effect {
    background: oklch(99.9% 0.001 0 / 85%);
    backdrop-filter: blur(16px);
    border: 1px solid oklch(90% 0.02 180 / 20%);
  }
  
  .dark .glass-effect {
    background: oklch(12% 0.02 250 / 90%);
    backdrop-filter: blur(16px);
    border: 1px solid oklch(22% 0.02 250 / 40%);
  }
  
  /* Tech-themed animations */
  .pulse-tech {
    animation: pulse-tech 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  @keyframes pulse-tech {
    0%, 100% {
      box-shadow: 0 0 0 0 oklch(88% 0.15 95 / 60%);
    }
    50% {
      box-shadow: 0 0 0 12px oklch(88% 0.15 95 / 0%);
    }
  }
  
  /* Enhanced card hover effects */
  .card-hover {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .card-hover:hover {
    transform: translateY(-4px) scale(1.01);
    box-shadow: 0 20px 40px oklch(27% 0.05 250 / 15%);
  }
  
  .dark .card-hover:hover {
    box-shadow: 0 20px 40px oklch(0% 0 0 / 30%);
  }
  
  /* Better responsive text truncation */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* Custom styled scrollbar */
  .scrollbar-styled {
    scrollbar-width: thin;
    scrollbar-color: var(--muted) var(--background);
  }
  
  .scrollbar-styled::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-styled::-webkit-scrollbar-track {
    background: var(--background);
    border-radius: 3px;
  }
  
  .scrollbar-styled::-webkit-scrollbar-thumb {
    background: var(--muted);
    border-radius: 3px;
  }
  
  .scrollbar-styled::-webkit-scrollbar-thumb:hover {
    background: var(--muted-foreground);
  }
}
