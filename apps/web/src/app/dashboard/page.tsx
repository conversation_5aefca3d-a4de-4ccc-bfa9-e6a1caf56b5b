"use client";

import { useState, useEffect, useCallback, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { trpc } from "@/utils/trpc";
import { SearchBar } from "@/components/search-bar";
import { ContentTable } from "@/components/content-table";
import { Zap, Users, MessageCircle, TrendingUp } from "lucide-react";

interface Filters {
  contentType: string;
  category: string;
  sortBy: string;
  sortOrder: string;
}

function HomeContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Initialize state from URL parameters
  const [searchQuery, setSearchQuery] = useState(() => searchParams.get("q") || "");
  const [filters, setFilters] = useState<Filters>(() => ({
    contentType: searchParams.get("type") || "all",
    category: searchParams.get("category") || "all",
    sortBy: searchParams.get("sort") || "impressions",
    sortOrder: searchParams.get("order") || "desc"
  }));

  // Pagination state for each section
  const [twitterPage, setTwitterPage] = useState(1);
  const [marketingPage, setMarketingPage] = useState(1);
  const [testimonialsPage, setTestimonialsPage] = useState(1);

  // Update URL when state changes
  const updateURL = useCallback((newQuery: string, newFilters: Filters) => {
    const params = new URLSearchParams();
    
    // Only add non-default values to keep URL clean
    if (newQuery) params.set("q", newQuery);
    if (newFilters.contentType !== "all") params.set("type", newFilters.contentType);
    if (newFilters.category !== "all") params.set("category", newFilters.category);
    if (newFilters.sortBy !== "impressions") params.set("sort", newFilters.sortBy);
    if (newFilters.sortOrder !== "desc") params.set("order", newFilters.sortOrder);
    
    const queryString = params.toString();
    const newURL = queryString ? `/?${queryString}` : "/";
    
    // Update URL without refreshing the page
    router.replace(newURL, { scroll: false });
  }, [router]);

  // Sync URL when search query changes
  useEffect(() => {
    updateURL(searchQuery, filters);
  }, [searchQuery, filters, updateURL]);

  // Handle browser back/forward navigation
  useEffect(() => {
    const handlePopState = () => {
      const currentParams = new URLSearchParams(window.location.search);
      setSearchQuery(currentParams.get("q") || "");
      setFilters({
        contentType: currentParams.get("type") || "all",
        category: currentParams.get("category") || "all",
        sortBy: currentParams.get("sort") || "impressions",
        sortOrder: currentParams.get("order") || "desc"
      });
    };

    window.addEventListener("popstate", handlePopState);
    return () => window.removeEventListener("popstate", handlePopState);
  }, []);

  // Queries for all sections with pagination
  const twitterContentQuery = trpc.getAllTwitterContent.useQuery({ 
    page: twitterPage, 
    limit: 9 
  });
  const marketingCaseStudiesQuery = trpc.getMarketingCaseStudies.useQuery({ 
    page: marketingPage, 
    limit: 9 
  });
  const testimonialsQuery = trpc.getTestimonials.useQuery({ 
    page: testimonialsPage, 
    limit: 9 
  });

  // Keep the filtered content query for search functionality
  const filteredContentQuery = trpc.getFilteredContent.useQuery({
    search: searchQuery,
    contentType: filters.contentType as "all" | "spaces" | "tweets" | "marketing",
    category: filters.category,
    sortBy: filters.sortBy as "impressions" | "date" | "likes" | "retweets",
    sortOrder: filters.sortOrder as "desc" | "asc",
    limit: 30
  });

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const handleFilter = (filterType: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const handleSort = (sortBy: string, sortOrder?: string) => {
    setFilters(prev => ({
      ...prev,
      sortBy,
      sortOrder: sortOrder || prev.sortOrder
    }));
  };

  const getDisplayTitle = () => {
    const typeMap = {
      all: "Twitter Spaces & Tweets",
      spaces: "Twitter Spaces",
      tweets: "Tweets",
      marketing: "Sponsored Content"
    };
    
    let title = typeMap[filters.contentType as keyof typeof typeMap] || "Content";
    
    if (filters.category !== "all") {
      title += ` • ${filters.category.toUpperCase()}`;
    }
    
    if (searchQuery) {
      title = `Search: "${searchQuery}"`;
    }
    
    return title;
  };

  const isSearchActive = searchQuery || filters.contentType !== "all" || filters.category !== "all";

  return (
    <div className="min-h-screen gradient-tech">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/3 via-transparent to-accent/3" />
        <div className="relative content-container py-20 lg:py-32">
          {/* Main Hero Content */}
          <div className="center-all flex-col text-center space-y-12">
            {/* Title */}
            <div className="space-y-6">
              <h1 className="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-bold tracking-tight leading-tight">
                <div className="bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
                  THE PATH TO
                </div>
                <div className="text-attention-blue mt-2">
                  ATTENTION
                </div>
              </h1>
              
              <p className="text-muted-foreground text-xl sm:text-2xl max-w-3xl mx-auto leading-relaxed font-medium">
                Discover our most impactful content across all platforms
              </p>
            </div>
            
            {/* Search Bar */}
            <div className="w-full max-w-5xl mx-auto flex flex-col items-center">
              <SearchBar
                onSearch={handleSearch}
                onFilter={handleFilter}
                onSort={handleSort}
                placeholder="Search across all content..."
                filters={filters}
                searchQuery={searchQuery}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Content Sections */}
      <section className="content-container pb-20">
        {isSearchActive ? (
          // Show filtered results when search is active
          <div className="glass-effect rounded-3xl p-10 card-hover">
            <ContentTable
              title={getDisplayTitle()}
              data={filteredContentQuery.data || []}
              titleColor="chart-1"
              isLoading={filteredContentQuery.isLoading}
            />
          </div>
        ) : (
          // Show all sections when no search is active
          <div className="space-y-20">
            {/* Twitter Spaces & Tweets Section */}
            <div className="glass-effect rounded-3xl p-10 card-hover">
              <ContentTable
                title="Twitter Spaces & Tweets"
                data={twitterContentQuery.data?.data || []}
                titleColor="chart-1"
                isLoading={twitterContentQuery.isLoading}
                currentPage={twitterPage}
                totalItems={twitterContentQuery.data?.total || 0}
                onPageChange={setTwitterPage}
                itemsPerPage={9}
              />
            </div>

            {/* Marketing Case Studies Section */}
            <div className="glass-effect rounded-3xl p-10 card-hover">
              <ContentTable
                title="Marketing Case Studies"
                data={marketingCaseStudiesQuery.data?.data || []}
                titleColor="chart-2"
                isLoading={marketingCaseStudiesQuery.isLoading}
                currentPage={marketingPage}
                totalItems={marketingCaseStudiesQuery.data?.total || 0}
                onPageChange={setMarketingPage}
                itemsPerPage={9}
              />
            </div>

            {/* Testimonials Section */}
            <div className="glass-effect rounded-3xl p-10 card-hover">
              <ContentTable
                title="Testimonials"
                data={testimonialsQuery.data?.data || []}
                titleColor="chart-3"
                isLoading={testimonialsQuery.isLoading}
                currentPage={testimonialsPage}
                totalItems={testimonialsQuery.data?.total || 0}
                onPageChange={setTestimonialsPage}
                itemsPerPage={9}
              />
            </div>
          </div>
        )}
      </section>
    </div>
  );
}

export default function Home() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <HomeContent />
    </Suspense>
  );
}
