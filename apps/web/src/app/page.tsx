"use client";

import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ArrowRight, BarChart3, Users, Target, Zap, ExternalLink, Eye, Heart, Repeat2, Twitter, Linkedin, Youtube, Globe } from "lucide-react";
import { trpc } from "@/utils/trpc";

function formatNumber(num: number) {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return num.toString();
}

export default function LandingPage() {
  const router = useRouter();
  const { data: impactMetrics, isLoading } = trpc.getImpactMetrics.useQuery();
  const { data: featuredContent, isLoading: featuredLoading } = trpc.getFeaturedContent.useQuery();

  const handleDashboardClick = () => {
    console.log('Dashboard button clicked - attempting navigation...');
    try {
      router.push('/dashboard');
      // Fallback navigation if router.push fails
      setTimeout(() => {
        if (window.location.pathname !== '/dashboard') {
          console.log('Router.push failed, using window.location');
          window.location.href = '/dashboard';
        }
      }, 100);
    } catch (error) {
      console.error('Navigation error:', error);
      window.location.href = '/dashboard';
    }
  };

  return (
    <div className="min-h-screen gradient-tech">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5" />
        <div className="relative content-container py-24 lg:py-32">
          <div className="center-all flex-col text-center space-y-12 max-w-6xl mx-auto">
            {/* Main Hero Content */}
            <div className="space-y-8">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold tracking-tight leading-tight">
                <div className="bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
                  BUILDING THE
                </div>
                <div className="text-attention-blue mt-2">
                  FUTURE OF ATTENTION
                </div>
              </h1>
              
              <p className="text-muted-foreground text-xl sm:text-2xl max-w-4xl mx-auto leading-relaxed font-medium">
                We create content that captures attention, drives engagement, and builds meaningful connections across digital platforms
              </p>
            </div>
            
            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button 
                size="lg" 
                className="flex items-center gap-2 text-lg px-8 py-6 hover:scale-105 transition-all duration-300"
                onClick={handleDashboardClick}
              >
                <BarChart3 className="h-5 w-5" />
                Explore Dashboard
                <ArrowRight className="h-5 w-5" />
              </Button>
              <Button variant="outline" size="lg" className="flex items-center gap-2 text-lg px-8 py-6 hover:scale-105 transition-all duration-300">
                Learn More
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Impact Metrics Section */}
      <section className="content-container py-24">
        <div className="glass-effect rounded-3xl p-10 card-hover">
          <div className="text-center space-y-12">
            <div className="space-y-4">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold">Our Impact</h2>
              <p className="text-muted-foreground text-xl max-w-3xl mx-auto">
                Numbers that showcase our reach and influence across platforms
              </p>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 sm:gap-12 lg:gap-20 max-w-6xl mx-auto">
              <div className="space-y-4 text-center px-4">
                <div className="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-bold text-chart-1 leading-none tracking-tight min-h-[80px] sm:min-h-[100px] lg:min-h-[120px] flex items-center justify-center">
                  {isLoading ? (
                    <div className="animate-pulse bg-chart-1/20 rounded-2xl h-16 sm:h-20 lg:h-24 w-full max-w-[200px]"></div>
                  ) : (
                    `${formatNumber(impactMetrics?.totalImpressions || 0)}+`
                  )}
                </div>
                <div className="text-muted-foreground text-base sm:text-lg lg:text-xl font-semibold tracking-wide px-2">
                  Total Impressions
                </div>
              </div>
              <div className="space-y-4 text-center px-4">
                <div className="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-bold text-chart-2 leading-none tracking-tight min-h-[80px] sm:min-h-[100px] lg:min-h-[120px] flex items-center justify-center">
                  {isLoading ? (
                    <div className="animate-pulse bg-chart-2/20 rounded-2xl h-16 sm:h-20 lg:h-24 w-full max-w-[200px]"></div>
                  ) : (
                    `${impactMetrics?.totalContent || 0}+`
                  )}
                </div>
                <div className="text-muted-foreground text-base sm:text-lg lg:text-xl font-semibold tracking-wide px-2">
                  Content Pieces
                </div>
              </div>
              <div className="space-y-4 text-center px-4">
                <div className="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-bold text-chart-3 leading-none tracking-tight min-h-[80px] sm:min-h-[100px] lg:min-h-[120px] flex items-center justify-center">
                  {isLoading ? (
                    <div className="animate-pulse bg-chart-3/20 rounded-2xl h-16 sm:h-20 lg:h-24 w-full max-w-[200px]"></div>
                  ) : (
                    impactMetrics?.avgEngagementRate !== null && impactMetrics?.avgEngagementRate !== undefined ? 
                      `${impactMetrics.avgEngagementRate}%` : 
                      "N/A"
                  )}
                </div>
                <div className="text-muted-foreground text-base sm:text-lg lg:text-xl font-semibold tracking-wide px-2">
                  Avg Engagement Rate
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="content-container py-24">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-start lg:items-center">
          <div className="space-y-8">
            <div className="space-y-4">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold">Who We Are</h2>
              <p className="text-muted-foreground text-lg sm:text-xl leading-relaxed">
                We're content creators, strategists, and digital storytellers focused on mastering 
                the attention economy through data-driven insights and creative excellence.
              </p>
            </div>
            
            <div className="space-y-8">
              <div className="flex items-start gap-4 group">
                <div className="p-3 rounded-xl bg-chart-1/10 group-hover:bg-chart-1/20 transition-colors shrink-0">
                  <Target className="h-6 w-6 text-chart-1" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg mb-1">Strategic Content</h3>
                  <p className="text-muted-foreground text-sm sm:text-base leading-relaxed">Every piece is crafted with purpose and data-backed insights</p>
                </div>
              </div>
              
              <div className="flex items-start gap-4 group">
                <div className="p-3 rounded-xl bg-chart-2/10 group-hover:bg-chart-2/20 transition-colors shrink-0">
                  <Users className="h-6 w-6 text-chart-2" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg mb-1">Community Building</h3>
                  <p className="text-muted-foreground text-sm sm:text-base leading-relaxed">Fostering meaningful connections and engagement</p>
                </div>
              </div>
              
              <div className="flex items-start gap-4 group">
                <div className="p-3 rounded-xl bg-chart-3/10 group-hover:bg-chart-3/20 transition-colors shrink-0">
                  <Zap className="h-6 w-6 text-chart-3" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg mb-1">Performance Driven</h3>
                  <p className="text-muted-foreground text-sm sm:text-base leading-relaxed">Continuous optimization based on real metrics</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="glass-effect rounded-3xl p-8 lg:p-10 space-y-8">
            <h3 className="text-2xl sm:text-3xl font-bold">What We Do</h3>
            <div className="space-y-6">
              <div className="p-6 rounded-2xl bg-background/50 hover:bg-background/70 transition-colors group">
                <h4 className="font-semibold text-lg mb-3">Twitter Spaces & Content</h4>
                <p className="text-muted-foreground leading-relaxed">Live discussions and curated tweets that drive engagement</p>
              </div>
              <div className="p-6 rounded-2xl bg-background/50 hover:bg-background/70 transition-colors group">
                <h4 className="font-semibold text-lg mb-3">Marketing Case Studies</h4>
                <p className="text-muted-foreground leading-relaxed">In-depth analysis of successful campaigns and strategies</p>
              </div>
              <div className="p-6 rounded-2xl bg-background/50 hover:bg-background/70 transition-colors group">
                <h4 className="font-semibold text-lg mb-3">Community Testimonials</h4>
                <p className="text-muted-foreground leading-relaxed">Authentic feedback and success stories from our audience</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Content Section */}
      <section className="content-container py-24">
        <div className="space-y-12">
          <div className="text-center space-y-4">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold">Greatest Hits</h2>
            <p className="text-muted-foreground text-lg sm:text-xl max-w-3xl mx-auto leading-relaxed">
              Our most impactful content pieces that captured attention and drove meaningful engagement
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
            {featuredLoading ? (
              Array.from({ length: 6 }).map((_, i) => (
                <Card key={i} className="p-6 lg:p-8 glass-effect animate-pulse">
                  <div className="space-y-4">
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="h-3 bg-muted rounded w-1/2"></div>
                    <div className="flex gap-4">
                      <div className="h-3 bg-muted rounded w-16"></div>
                      <div className="h-3 bg-muted rounded w-16"></div>
                      <div className="h-3 bg-muted rounded w-16"></div>
                    </div>
                  </div>
                </Card>
              ))
            ) : (
              featuredContent?.slice(0, 6).map((item) => (
                <Card key={item.id} className="p-6 lg:p-8 glass-effect card-hover group hover:scale-[1.02] transition-all duration-300">
                  <div className="space-y-5">
                    <div className="space-y-2">
                      <div className="flex items-start justify-between gap-2">
                        <h3 className="font-semibold text-lg lg:text-xl leading-tight line-clamp-2">
                          {item.content_title || `Content from @${item.content_account}`}
                        </h3>
                        <a 
                          href={item.content_link} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="opacity-0 group-hover:opacity-100 transition-all duration-300 p-2 rounded-lg hover:bg-background/50"
                        >
                          <ExternalLink className="h-4 w-4 text-muted-foreground hover:text-foreground transition-colors" />
                        </a>
                      </div>
                      <p className="text-sm text-muted-foreground">@{item.content_account}</p>
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Eye className="h-4 w-4" />
                        <span>{formatNumber(item.twitter_impressions)}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Heart className="h-4 w-4" />
                        <span>{formatNumber(item.twitter_likes)}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Repeat2 className="h-4 w-4" />
                        <span>{formatNumber(item.twitter_retweets)}</span>
                      </div>
                    </div>
                    
                    {item.content_categories && item.content_categories.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {item.content_categories.slice(0, 2).map((category) => (
                          <span 
                            key={category}
                            className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full"
                          >
                            {category}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </Card>
              ))
            )}
          </div>
          
          <div className="text-center">
            <Button 
              variant="outline" 
              size="lg" 
              className="flex items-center gap-2 hover:scale-105 transition-all duration-300"
              onClick={handleDashboardClick}
            >
              View All Content
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </section>

      {/* Social Links Section */}
      <section className="content-container py-24">
        <div className="glass-effect rounded-3xl p-10">
          <div className="text-center space-y-8">
            <div className="space-y-4">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold">Connect With Us</h2>
              <p className="text-muted-foreground text-lg sm:text-xl max-w-3xl mx-auto leading-relaxed">
                Follow our journey across platforms and stay updated with our latest content
              </p>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 max-w-3xl mx-auto">
              <a 
                href="https://twitter.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex flex-col items-center gap-3 p-6 sm:p-8 rounded-2xl bg-background/50 hover:bg-background/80 hover:scale-105 transition-all duration-300 group min-h-[120px] sm:min-h-[140px]"
              >
                <div className="p-3 rounded-full bg-blue-500/10 group-hover:bg-blue-500/20 transition-colors">
                  <Twitter className="h-6 w-6 text-blue-500" />
                </div>
                <span className="font-semibold">Twitter</span>
              </a>
              
              <a 
                href="https://linkedin.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex flex-col items-center gap-3 p-6 sm:p-8 rounded-2xl bg-background/50 hover:bg-background/80 hover:scale-105 transition-all duration-300 group min-h-[120px] sm:min-h-[140px]"
              >
                <div className="p-3 rounded-full bg-blue-600/10 group-hover:bg-blue-600/20 transition-colors">
                  <Linkedin className="h-6 w-6 text-blue-600" />
                </div>
                <span className="font-semibold">LinkedIn</span>
              </a>
              
              <a 
                href="https://youtube.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex flex-col items-center gap-3 p-6 sm:p-8 rounded-2xl bg-background/50 hover:bg-background/80 hover:scale-105 transition-all duration-300 group min-h-[120px] sm:min-h-[140px]"
              >
                <div className="p-3 rounded-full bg-red-500/10 group-hover:bg-red-500/20 transition-colors">
                  <Youtube className="h-6 w-6 text-red-500" />
                </div>
                <span className="font-semibold">YouTube</span>
              </a>
              
              <a 
                href="https://example.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex flex-col items-center gap-3 p-6 sm:p-8 rounded-2xl bg-background/50 hover:bg-background/80 hover:scale-105 transition-all duration-300 group min-h-[120px] sm:min-h-[140px]"
              >
                <div className="p-3 rounded-full bg-green-500/10 group-hover:bg-green-500/20 transition-colors">
                  <Globe className="h-6 w-6 text-green-500" />
                </div>
                <span className="font-semibold">Website</span>
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="content-container py-24">
        <div className="glass-effect rounded-3xl p-12 lg:p-16 text-center">
          <div className="space-y-8 max-w-3xl mx-auto">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold">Ready to Explore?</h2>
            <p className="text-muted-foreground text-lg sm:text-xl leading-relaxed">
              Dive into our comprehensive dashboard to discover our most impactful content, 
              track performance metrics, and see what drives attention in today's digital landscape.
            </p>
            <div className="flex justify-center">
              <Button 
                size="lg" 
                className="flex items-center gap-2 text-lg px-8 py-6 hover:scale-105 transition-all duration-300"
                onClick={handleDashboardClick}
              >
                <BarChart3 className="h-5 w-5" />
                Explore Dashboard
                <ArrowRight className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}