{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate"}, "dependencies": {"@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.0", "@tanstack/react-form": "^1.12.3", "@tanstack/react-query": "^5.80.5", "@trpc/client": "^11.4.2", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "lucide-react": "^0.487.0", "next": "15.3.0", "pg": "^8.14.1", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "zod": "^4.0.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/pg": "^8.11.11", "drizzle-kit": "^0.31.2", "tailwindcss": "^4.1.10", "typescript": "^5", "@tanstack/react-query-devtools": "^5.80.5"}}